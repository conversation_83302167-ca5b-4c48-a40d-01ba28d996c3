<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meiye.api.mapper.MemberMapper">

    <resultMap type="Member" id="MemberResult">
        <result property="id"    column="id"    />
        <result property="sn"    column="sn"    />

        <result property="memberLevelId"    column="member_level_id"    />
        <result property="rootStoreId"    column="root_store_id"    />
        <result property="storeId"    column="store_id"    />
        <result property="name"    column="name"    />
        <result property="gender"    column="gender"    />
        <result property="phone"    column="phone"    />
        <result property="password"    column="password"    />
        <result property="oauth"    column="oauth"    />
        <result property="realBalance"    column="real_balance"    />
        <result property="giftBalance"    column="gift_balance"    />
        <result property="totalCash"    column="total_cash"    />
        <result property="lastConsumption"    column="last_consumption"    />
        <result property="isActive"    column="is_active"    />
        <result property="createdByEmployeeId"    column="created_by_employee_id"    />
        <result property="createdAt"    column="created_at"    />
        <result property="memberLevelName"    column="member_level_name"    />
        <result property="createdByEmployeeName"    column="created_by_employee_name"    />
        <result property="rootStoreName"    column="root_store_name"    />
        <result property="storeName"    column="store_name"    />
    </resultMap>

    <sql id="selectMemberVo">
        select m.id,m.sn, m.member_level_id, m.root_store_id, m.store_id, m.name, m.gender, m.phone, m.password, m.oauth,
               m.real_balance, m.gift_balance, m.total_cash, m.last_consumption, m.is_active, m.created_by_employee_id, m.created_at,
               ml.name as member_level_name,
               su.nick_name as created_by_employee_name,
               rs.name as root_store_name,
               s.name as store_name
        from member m
                 left join member_level ml on m.member_level_id = ml.id
                 left join sys_user su on m.created_by_employee_id = su.user_id
                 left join store rs on m.root_store_id = rs.id
                 left join store s on m.store_id = s.id
    </sql>
    <select id="selectMemberList" parameterType="Member" resultMap="MemberResult">
        <include refid="selectMemberVo"/>
        <where>
            <if test="sn!= null"> and m.sn = #{sn}</if>
            <if test="memberLevelId != null "> and m.member_level_id = #{memberLevelId}</if>
            <if test="rootStoreId != null ">
                and (m.root_store_id = #{rootStoreId} 
                     or m.store_id in (select id from store where parent_id = #{rootStoreId})
                     or m.store_id = #{rootStoreId})
            </if>
            <if test="rootStoreId == null and storeId != null "> and m.store_id = #{storeId}</if>
            <if test="name != null  and name != ''"> and m.name like concat('%', #{name}, '%')</if>
            <if test="gender != null  and gender != ''"> and m.gender = #{gender}</if>
            <if test="phone != null  and phone != ''"> and m.phone  like concat('%', #{phone}, '%')</if>
            <if test="memberLevelName != null and memberLevelName != ''"> and ml.name like concat('%', #{memberLevelName}, '%')</if>
            <if test="beginCreatedAt != null and endCreatedAt != null"> and m.created_at between #{beginCreatedAt} and #{endCreatedAt}</if>
            <if test="beginCreatedAt != null and endCreatedAt == null"> and m.created_at &gt;= #{beginCreatedAt}</if>
            <if test="beginCreatedAt == null and endCreatedAt != null"> and m.created_at &lt;= #{endCreatedAt}</if>
        </where>
        order by m.created_at desc
    </select>

    <select id="selectMemberById" parameterType="Long" resultMap="MemberResult">
        select m.id,m.sn, m.member_level_id, m.root_store_id, m.store_id, m.name, m.gender, m.phone, m.password, m.oauth,
               m.real_balance, m.gift_balance, m.total_cash, m.last_consumption, m.is_active, m.created_by_employee_id, m.created_at,
               ml.name as member_level_name,
               su.nick_name as created_by_employee_name,
               rs.name as root_store_name,
               s.name as store_name
        from member m
                 left join member_level ml on m.member_level_id = ml.id
                 left join sys_user su on m.created_by_employee_id = su.user_id
                 left join store rs on m.root_store_id = rs.id
                 left join store s on m.store_id = s.id
        where m.id = #{id}
    </select>

    <select id="selectAllMember" resultMap="MemberResult">
        <include refid="selectMemberVo"/>
        order by m.id
    </select>

    <insert id="insertMember" parameterType="Member"  useGeneratedKeys="true" keyProperty="id">
        insert into member
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sn != null">sn,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="gender != null and gender != ''">gender,</if>
            <if test="phone != null and phone != ''">phone,</if>
            <if test="password != null and password != ''">password,</if>
            <if test="memberLevelId != null">member_level_id,</if>
            <if test="storeId != null">store_id,</if>
            <if test="rootStoreId != null">root_store_id,</if>
            <if test="giftBalance != null">gift_balance,</if>
            <if test="realBalance != null">real_balance,</if>
            <if test="createdByEmployeeId != null">created_by_employee_id,</if>
            <if test="createdAt != null">created_at,</if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sn != null">#{sn},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="gender != null and gender != ''">#{gender},</if>
            <if test="phone != null and phone != ''">#{phone},</if>
            <if test="password != null and password != ''">#{password},</if>
            <if test="memberLevelId != null">#{memberLevelId},</if>
            <if test="storeId != null">#{storeId},</if>
            <if test="rootStoreId != null">#{rootStoreId},</if>
            <if test="giftBalance != null">#{giftBalance},</if>
            <if test="realBalance != null">#{realBalance},</if>
            <if test="createdByEmployeeId != null">#{createdByEmployeeId},</if>
            <if test="createdAt != null">#{createdAt},</if>
        </trim>
    </insert>

    <update id="updateMember" parameterType="Member">
        update member
        <trim prefix="SET" suffixOverrides=",">
            <if test="sn != null">sn = #{sn},</if>
            <if test="memberLevelId != null">member_level_id = #{memberLevelId},</if>
            <if test="rootStoreId != null">root_store_id = #{rootStoreId},</if>
            <if test="storeId != null">store_id = #{storeId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="phone != null and phone != ''">phone = #{phone},</if>
            <if test="password != null">password = #{password},</if>
            <if test="oauth != null">oauth = #{oauth},</if>
            <if test="realBalance != null">real_balance = #{realBalance},</if>
            <if test="giftBalance != null">gift_balance = #{giftBalance},</if>
            <if test="totalCash != null">total_cash = #{totalCash},</if>
            <if test="lastConsumption != null">last_consumption = #{lastConsumption},</if>
            <if test="isActive != null">is_active = #{isActive},</if>
            <if test="createdByEmployeeId != null">created_by_employee_id = #{createdByEmployeeId},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMemberById" parameterType="Long">
        delete from member where id = #{id}
    </delete>

    <delete id="deleteMemberByIds" parameterType="String">
        delete from member where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectMemberLevelOptions" resultType="MemberLevel">
        select id, name from member_level
        where 1=1
        order by level asc
    </select>

    <select id="getStoreIdByName" resultType="Long">
        select id from store where name = #{storeName}
    </select>

    <!-- <select id="getStoreRootId" resultType="Long">
        select id from store where parent_id = 0 or parent_id is null limit 1
    </select> -->
    <select id="queryMemberList" resultType="com.meiye.api.vo.MemberVO">
        SELECT
            m.id id,
            m.member_level_id memberLevelId,
            ml.`name` memberLevelName,
            m.sn,
            m.`name`,
            m.phone
        FROM
            member m
                LEFT JOIN member_level ml ON m.member_level_id = ml.id
    </select>

    <select id="getYearMemberNum" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM member
        WHERE YEAR(created_at) = YEAR(NOW())
          AND MONTH(created_at) = MONTH(NOW())
    </select>

    <select id="getMaxMemberSn" parameterType="String" resultType="String">
        SELECT sn
        FROM member
        WHERE sn LIKE CONCAT(#{snPrefix}, '%')
        ORDER BY sn DESC
            LIMIT 1
    </select>
</mapper>
