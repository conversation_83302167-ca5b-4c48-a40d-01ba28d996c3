<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meiye.api.mapper.ProductMapper">

  <resultMap type="Product" id="ProductResult">
    <result property="id"    column="id"    />
    <result property="rootStoreId"    column="root_store_id"    />
    <result property="categoryCode"    column="category_code"    />
    <result property="name"    column="name"    />
    <result property="price"    column="price"    />
    <result property="canUseBalance"    column="can_use_balance"    />
    <result property="status"    column="status"    />
    <result property="createdByEmployeeId"    column="created_by_employee_id"    />
    <result property="createdAt"    column="created_at"    />
    <result property="categoryId"    column="category_id"    />
    <result property="unit"    column="unit"    />
    <result property="isMain"    column="is_main"    />
  </resultMap>

  <sql id="selectProductVo">
    select id, root_store_id, category_code, name, price, can_use_balance, status, created_by_employee_id, created_at, category_id, unit, is_main from product
  </sql>

  <select id="selectServiceListFFF" parameterType="Product" resultMap="ProductResult">
    SELECT p.*
    FROM product p
    INNER JOIN product_category c
    ON p.category_id = c.id
    AND c.type = 'service'
    <where>
      <if test="rootStoreId != null "> and p.root_store_id in
        <foreach item="rootStoreId" collection="array" open="(" separator="," close=")">
          #{rootStoreId}
        </foreach>
           </if>
      <if test="categoryCode != null "> and p.category_code = #{categoryCode}</if>
      <if test="name != null  and name != ''"> and p.name like concat('%', #{name}, '%')</if>
      <if test="price != null "> and p.price = #{price}</if>
      <if test="priceMin != null">
        AND p.price &gt;= #{priceMin}
      </if>
      <if test="priceMax != null">
        AND p.price &lt;= #{priceMax}
      </if>
      <if test="canUseBalance != null "> and p.can_use_balance = #{canUseBalance}</if>
      <if test="status != null  and status != ''"> and p.status = #{status}</if>
      <if test="createdByEmployeeId != null "> and p.created_by_employee_id = #{createdByEmployeeId}</if>
      <if test="startDate != null">
        AND created_at &gt;= date_format(#{startDate}, '%Y-%m-%d %H:%i:%s')
      </if>
      <if test="endDate != null">
        AND created_at &lt;= date_format(#{endDate}, '%Y-%m-%d %H:%i:%s')
      </if>
      <if test="categoryId != null "> and p.category_id = #{categoryId}</if>
      <if test="isMain != null "> and p.is_main = #{isMain}</if>
    </where>
    ORDER BY created_at DESC
  </select>

  <select id="selectServiceList" parameterType="Product" resultMap="ProductResult">
SELECT
  a.*,
  b.store_id,
  b.sale_price,
  b.is_available,
  c.`name` AS CategoryName,
  c.id AS categoryId ,
  c.type
  FROM
  product a
  LEFT JOIN store_product b ON b.product_id = a.id
  LEFT JOIN product_category c ON a.category_id = c.id
  WHERE
    <if test="type != null">
  c.type =#{type}
    </if>
    <if test="rootStoreId != null">
  AND a.root_store_id  = #{rootStoreId}
    </if>
    <if test="storeId != null">
  AND b.store_id = #{storeId}
    </if>
    <if test="startDate != null">
      AND created_at &gt;= date_format(#{startDate}, '%Y-%m-%d %H:%i:%s')
    </if>
    <if test="endDate != null">
      AND created_at &lt;= date_format(#{endDate}, '%Y-%m-%d %H:%i:%s')
    </if>
  order by a.created_at desc
  </select>

  <select id="selectProductList" parameterType="Product" resultMap="ProductResult">
    SELECT p.*
    FROM product p
    INNER JOIN product_category c
    ON p.category_id = c.id
    AND c.type = 'goods'
    <where>
      <if test="rootStoreId != null "> and p.root_store_id = #{rootStoreId}</if>
      <if test="categoryCode != null "> and p.category_code = #{categoryCode}</if>
      <if test="name != null  and name != ''"> and p.name like concat('%', #{name}, '%')</if>
      <if test="price != null "> and p.price = #{price}</if>
      <if test="canUseBalance != null "> and p.can_use_balance = #{canUseBalance}</if>
      <if test="status != null  and status != ''"> and p.status = #{status}</if>
      <if test="createdByEmployeeId != null "> and p.created_by_employee_id = #{createdByEmployeeId}</if>
      <if test="startDate != null">
        AND created_at &gt;= date_format(#{startDate}, '%Y-%m-%d %H:%i:%s')
      </if>
      <if test="endDate != null">
        AND created_at &lt;= date_format(#{endDate}, '%Y-%m-%d %H:%i:%s')
      </if>
      <if test="categoryId != null "> and p.category_id = #{categoryId}</if>
      <if test="isMain != null "> and p.is_main = #{isMain}</if>
    </where>
    ORDER BY created_at DESC
  </select>

  <select id="selectCardList" parameterType="Product" resultMap="ProductResult">
    SELECT p.*
    FROM product p
    INNER JOIN product_category c
    ON p.category_id = c.id
    AND c.type = 'card'
    <where>
      <if test="rootStoreId != null "> and p.root_store_id = #{rootStoreId}</if>
      <if test="categoryCode != null "> and p.category_code = #{categoryCode}</if>
      <if test="name != null  and name != ''"> and p.name like concat('%', #{name}, '%')</if>
      <if test="price != null "> and p.price = #{price}</if>
      <if test="canUseBalance != null "> and p.can_use_balance = #{canUseBalance}</if>
      <if test="status != null  and status != ''"> and p.status = #{status}</if>
      <if test="createdByEmployeeId != null "> and p.created_by_employee_id = #{createdByEmployeeId}</if>
      <if test="startDate != null">
        AND created_at &gt;= date_format(#{startDate}, '%Y-%m-%d %H:%i:%s')
      </if>
      <if test="endDate != null">
        AND created_at &lt;= date_format(#{endDate}, '%Y-%m-%d %H:%i:%s')
      </if>
      <if test="categoryId != null "> and p.category_id = #{categoryId}</if>
      <if test="isMain != null "> and p.is_main = #{isMain}</if>
    </where>
    ORDER BY created_at DESC
  </select>

  <select id="selectProductById" parameterType="Long" resultMap="ProductResult">
    <include refid="selectProductVo"/>
    where id = #{id}
  </select>

  <insert id="insertProduct" parameterType="Product" useGeneratedKeys="true" keyProperty="id">
    insert into product
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="rootStoreId != null">root_store_id,</if>
      <if test="categoryCode != null">category_code,</if>
      <if test="name != null and name != ''">name,</if>
      <if test="price != null">price,</if>
      <if test="canUseBalance != null">can_use_balance,</if>
      <if test="status != null and status != ''">status,</if>
      <if test="createdByEmployeeId != null">created_by_employee_id,</if>
      <if test="createdAt != null">created_at,</if>
      <if test="categoryId != null">category_id,</if>
      <if test="unit != null and unit != ''">unit,</if>
      <if test="isMain != null">is_main,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="rootStoreId != null">#{rootStoreId},</if>
      <if test="categoryCode != null">#{categoryCode},</if>
      <if test="name != null and name != ''">#{name},</if>
      <if test="price != null">#{price},</if>
      <if test="canUseBalance != null">#{canUseBalance},</if>
      <if test="status != null and status != ''">#{status},</if>
      <if test="createdByEmployeeId != null">#{createdByEmployeeId},</if>
      <if test="createdAt != null">#{createdAt},</if>
      <if test="categoryId != null">#{categoryId},</if>
      <if test="unit != null and unit != ''">#{unit},</if>
      <if test="isMain != null">#{isMain},</if>
    </trim>
  </insert>

  <update id="updateProduct" parameterType="Product">
    update product
    <trim prefix="SET" suffixOverrides=",">
      <if test="rootStoreId != null">root_store_id = #{rootStoreId},</if>
      <if test="categoryCode != null">category_code = #{categoryCode},</if>
      <if test="name != null and name != ''">name = #{name},</if>
      <if test="price != null">price = #{price},</if>
      <if test="canUseBalance != null">can_use_balance = #{canUseBalance},</if>
      <if test="status != null and status != ''">status = #{status},</if>
      <if test="createdByEmployeeId != null">created_by_employee_id = #{createdByEmployeeId},</if>
      <if test="createdAt != null">created_at = #{createdAt},</if>
      <if test="categoryId != null">category_id = #{categoryId},</if>
      <if test="unit != null and unit != ''">unit = #{unit},</if>
      <if test="isMain != null">is_main = #{isMain},</if>
    </trim>
    where id = #{id}
  </update>

  <delete id="deleteProductById" parameterType="Long">
    delete from product where id = #{id}
  </delete>

  <delete id="deleteProductByIds" parameterType="String">
    delete from product where id in
    <foreach item="id" collection="array" open="(" separator="," close=")">
      #{id}
    </foreach>
  </delete>




  <select id="selectProductOption"  parameterType="Product" resultType="com.meiye.api.domain.Product">
    select p.id, p.name,p.price from product p
    left join  product_category c on p.category_id = c.id

    where p.root_store_id = #{rootStoreId}  and  p.status = 'enabled'

      <if test="type != null and type != '' ">
        and c.type = #{type}
      </if>
  </select>

  <select id="selectOptionSp"  parameterType="com.meiye.api.query.StoreProductQuery" resultType="com.meiye.api.vo.OptionVo">
    select p.id, p.name,p.price,c.type from store_product sp
    left join product p on p.id = sp.product_id
    left join  product_category c on p.category_id = c.id
    where sp.store_id = #{storeId}  and  sp.is_available = true

    <if test="type != null and type != '' ">
      and c.type = #{type}
    </if>

  </select>

</mapper>