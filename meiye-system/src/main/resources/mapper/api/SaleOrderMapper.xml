<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meiye.api.mapper.SaleOrderMapper">
  <resultMap id="BaseResultMap" type="com.meiye.api.domain.SaleOrder">
    <!--@mbg.generated-->
    <!--@Table sale_order-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="sn" jdbcType="VARCHAR" property="sn" />
    <result column="root_store_id" jdbcType="BIGINT" property="rootStoreId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="member_id" jdbcType="BIGINT" property="memberId" />
    <result column="temp_name" jdbcType="VARCHAR" property="tempName" />
    <result column="status" jdbcType="OTHER" property="status" />
    <result column="total_amount" jdbcType="DECIMAL" property="totalAmount" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="settled_at" jdbcType="TIMESTAMP" property="settledAt" />
    <result column="replace_discount" jdbcType="DECIMAL" property="replaceDiscount" />
    <result column="replace_discount_by" jdbcType="BIGINT" property="replaceDiscountBy" />
    <result column="replace_desciption" jdbcType="LONGVARCHAR" property="replaceDesciption" />
    <result column="paid_amount" jdbcType="DECIMAL" property="paidAmount" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, sn,root_store_id, store_id, member_id, `status`, total_amount, created_at, settled_at,
    replace_discount, replace_discount_by, replace_desciption, paid_amount,temp_name,remark
  </sql>

</mapper>