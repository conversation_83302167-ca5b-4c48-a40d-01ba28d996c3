<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.meiye.api.mapper.SaleOrderEmployeeMapper">
  <resultMap id="BaseResultMap" type="com.meiye.api.domain.SaleOrderEmployee">
    <!--@mbg.generated-->
    <!--@Table sale_order_employee-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="sale_order_detail_id" jdbcType="BIGINT" property="saleOrderDetailId" />
    <result column="employee_id" jdbcType="BIGINT" property="employeeId" />
    <result column="cp_department_id" jdbcType="BIGINT" property="cpDepartmentId" />
    <result column="cp_position_id" jdbcType="BIGINT" property="cpPositionId" />
    <result column="employee_rings" jdbcType="INTEGER" property="employeeRings" />
    <result column="audio_key" jdbcType="VARCHAR" property="audioKey" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="feedback" jdbcType="LONGVARCHAR" property="feedback" />
    <result column="description" jdbcType="LONGVARCHAR" property="description" />
    <result column="commission_value" jdbcType="DECIMAL" property="commissionValue" />
    <result column="audio_id" jdbcType="BIGINT" property="audioId" />
    <result column="created_at" jdbcType="TIMESTAMP" property="endTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, sale_order_detail_id, employee_id, cp_department_id, cp_position_id, employee_rings, 
    audio_key, start_time, end_time, feedback, description, commission_value, audio_id, created_at
  </sql>
</mapper>