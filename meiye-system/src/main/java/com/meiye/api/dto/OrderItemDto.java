package com.meiye.api.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
public class OrderItemDto implements Serializable {
    @NonNull
    private Long storeProductId;
    private List<ServiceLogDto> serviceLogList;
    /*service、goods、card*/
    private String categoryType;
    private BigDecimal price;

    private Integer quantity;
    private String remark;
}
