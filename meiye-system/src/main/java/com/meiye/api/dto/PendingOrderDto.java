package com.meiye.api.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
public class PendingOrderDto implements Serializable {
    /*会员id*/

    private Long memberId;
    private String tempName;
    private Long storeId;
    private Long userId;
    private BigDecimal totalAmount;

    /*操作类型
    *  'pendingOrder': '挂单',
        'checkout': '结账',
        'addCard': '开卡',
        'renewalCard': '续卡',
        'cancelCard': '退卡',
        'transferCard': '转卡',
    *
    * */
    private String type;
    private String remark;
    private List<OrderItemDto> orderItemList;


}
