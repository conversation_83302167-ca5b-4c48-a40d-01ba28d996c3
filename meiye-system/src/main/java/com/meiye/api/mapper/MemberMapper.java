package com.meiye.api.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meiye.api.domain.Member;
import com.meiye.api.query.MemberQuery;
import com.meiye.api.vo.MemberVO;

import java.util.List;

/**
 * 会员Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
public interface MemberMapper extends BaseMapper<Member>
{
    /**
     * 查询会员
     *
     * @param id 会员主键
     * @return 会员
     */
    public Member selectMemberById(Long id);

    /**
     * 查询会员列表
     *
     * @param member 会员
     * @return 会员集合
     */
    public List<Member> selectMemberList(Member member);
    
    /**
     * 查询所有会员列表（不分页）
     * 
     * @return 会员集合
     */
    public List<Member> selectAllMember();

    /**
     * 根据店面名称获取店面ID
     *
     * @param storeName 店面名称
     * @return 店面ID
     */
    Long getStoreIdByName(String storeName);

    /**
     * 新增会员
     *
     * @param member 会员
     * @return 结果
     */
    public int insertMember(Member member);

    /**
     * 修改会员
     *
     * @param member 会员
     * @return 结果
     */
    public int updateMember(Member member);

    List<MemberVO> queryMemberList(MemberQuery query);
    /**
     * 删除会员
     *
     * @param id 会员主键
     * @return 结果
     */
    public int deleteMemberById(Long id);

    /**
     * 批量删除会员
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMemberByIds(Long[] ids);

    /**
     * 获取主店面ID
     *
     * @return 主店面ID
     */
    public Long getStoreRootId();

    /**
     * 根据前缀获取最大的会员编号
     *
     * @param snPrefix 编号前缀
     * @return 最大编号
     */
    public String getMaxMemberSn(String snPrefix);

    Integer getYearMemberNum();
}