package com.meiye.api.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meiye.api.domain.SaleOrder;
import lombok.NonNull;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface SaleOrderMapper extends BaseMapper<SaleOrder> {
    @Select("select post_id from sys_user_post where user_id = #{employeeId}")
    Long selectPostIdByEmployeeId(@NonNull Long employeeId);
}