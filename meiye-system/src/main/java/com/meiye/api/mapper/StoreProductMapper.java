package com.meiye.api.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meiye.api.domain.StoreProduct;
import com.meiye.api.query.StoreProductQuery;
import com.meiye.api.vo.ProductSnapshot;
import com.meiye.api.vo.StoreProductVO;
import lombok.NonNull;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface StoreProductMapper extends BaseMapper<StoreProduct> {
    /**
     * 查询店面实际销售物
     *
     * @param id 店面实际销售物主键
     * @return 店面实际销售物
     */
    public StoreProduct selectStoreProductById(Long id);

    /**
     * 查询店面实际销售物列表
     *
     * @param storeProduct 店面实际销售物
     * @return 店面实际销售物集合
     */
    public List<StoreProduct> selectStoreProductList(StoreProduct storeProduct);

    /**
     * 新增店面实际销售物
     *
     * @param storeProduct 店面实际销售物
     * @return 结果
     */
    public int insertStoreProduct(StoreProduct storeProduct);

    /**
     * 修改店面实际销售物
     *
     * @param storeProduct 店面实际销售物
     * @return 结果
     */
    public int updateStoreProduct(StoreProduct storeProduct);

    /**
     * 删除店面实际销售物
     *
     * @param id 店面实际销售物主键
     * @return 结果
     */
    public int deleteStoreProductById(Long id);

    /**
     * 删除店面实际销售物
     *
     * @param id 店面实际销售物主键
     * @return 结果
     */
    public int deleteStoreProductByPId(Long id);

    /**
     * 批量删除店面实际销售物
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteStoreProductByIds(Long[] ids);
    List<StoreProductVO> queryStoreProductList(@Param("query") StoreProductQuery storeProductQuery);

    ProductSnapshot getProductSnapshot(Long storeProductId);
}