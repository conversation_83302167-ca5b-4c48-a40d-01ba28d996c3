package com.meiye.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 服务实施表
 */
@ApiModel(description="服务实施表")
@Data
@TableName(value = "sale_order_employee")
public class SaleOrderEmployee {
    /**
     * 记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="记录ID")
    private Long id;

    /**
     * 订单明细ID
     */
    @TableField(value = "sale_order_detail_id")
    @ApiModelProperty(value="订单明细ID")
    private Long saleOrderDetailId;

    /**
     * 服务员工ID
     */
    @TableField(value = "employee_id")
    @ApiModelProperty(value="服务员工ID")
    private Long employeeId;

    /**
     * 服务员工所在的部门
     */
    @TableField(value = "cp_department_id")
    @ApiModelProperty(value="服务员工所在的部门")
    private Long cpDepartmentId;

    /**
     * 服务员工所在的职位
     */
    @TableField(value = "cp_position_id")
    @ApiModelProperty(value="服务员工所在的职位")
    private Long cpPositionId;

    /**
     * 服务员工名次
     */
    @TableField(value = "employee_rings")
    @ApiModelProperty(value="服务员工名次")
    private Integer employeeRings;

    /**
     * 音频信息的标识符
     */
    @TableField(value = "audio_key")
    @ApiModelProperty(value="音频信息的标识符")
    private String audioKey;

    /**
     * 开始时间
     */
    @TableField(value = "start_time")
    @ApiModelProperty(value="开始时间")
    private Date startTime;

    /**
     * 结束时间
     */
    @TableField(value = "end_time")
    @ApiModelProperty(value="结束时间")
    private Date endTime;

    /**
     * 客户反馈
     */
    @TableField(value = "feedback")
    @ApiModelProperty(value="客户反馈")
    private String feedback;

    /**
     * 服务备忘
     */
    @TableField(value = "description")
    @ApiModelProperty(value="服务备忘")
    private String description;

    /**
     * 提成金额
     */
    @TableField(value = "commission_value")
    @ApiModelProperty(value="提成金额")
    private BigDecimal commissionValue;

    /**
     * 服务过程对话文本ID
     */
    @TableField(value = "audio_id")
    @ApiModelProperty(value="服务过程对话文本ID")
    private Long audioId;

    @TableField(value = "created_at")
    @ApiModelProperty(value="结束时间")
    private Date createdAt;

}