package com.meiye.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 会员等级表
 */
@ApiModel(description="会员等级表")
@Data
@TableName(value = "member_level")
public class MemberLevel {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="id")
    private Long id;

    /**
     * 所属的主店面
     */
    @TableField(value = "root_store_id")
    @ApiModelProperty(value="所属的主店面")
    private Long rootStoreId;

    /**
     * 等级序号
     */
    @TableField(value = "`level`")
    @ApiModelProperty(value="等级序号")
    private Integer level;

    /**
     * 等级名称
     */
    @TableField(value = "`name`")
    @ApiModelProperty(value="等级名称")
    private String name;

    /**
     * 自动升级周期：FULL_CASH-总额(仅现金)，YEAR_CASH-按年(仅现金)，RECENTLY_YEAR_CASH-最近365天(仅现金)，
     * RECENTLY_2_QUARTERLY_CASH-最近半年(仅现金)，RECENTLY_QUARTERLY_CASH-最近一季度(仅现金)，
     * FULL_NO_CARD-总额(不包括购卡)，YEAR_NO_CARD-按年(不包括购卡)，RECENTLY_YEAR_NO_CARD-最近365天(不包括购卡)，
     * RECENTLY_2_QUARTERLY_NO_CARD-最近半年(不包括购卡)，RECENTLY_QUARTERLY_NO_CARD-最近一季度(不包括购卡)
     */
    @TableField(value = "auto_level_model")
    @ApiModelProperty(value="自动升级周期")
    private String autoLevelModel;

    /**
     * 消费折扣比例
     */
    @TableField(value = "discount_ratio")
    @ApiModelProperty(value="消费折扣比例")
    private BigDecimal discountRatio;

    /**
     * 消费额（现金消费达到后自动升级）
     */
    @TableField(value = "total_cash")
    @ApiModelProperty(value="消费额（现金消费达到后自动升级）")
    private BigDecimal totalCash;
    /**
     * 主店面名称（关联查询字段）
     */
    @TableField(exist = false)
    @ApiModelProperty(value="主店面名称")
    private String rootStoreName;


    public String getRootStoreName() {
        return rootStoreName;
    }

    public void setRootStoreName(String rootStoreName) {
        this.rootStoreName = rootStoreName;
    }
}