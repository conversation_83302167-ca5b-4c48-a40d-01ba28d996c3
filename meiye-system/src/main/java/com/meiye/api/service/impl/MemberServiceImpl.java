package com.meiye.api.service.impl;

import java.util.List;
import java.util.Date;
import java.util.Set;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meiye.api.domain.*;
import com.meiye.api.query.MemberQuery;
import com.meiye.api.service.*;
import com.meiye.api.vo.MemberVO;
import com.meiye.common.core.domain.entity.SysUser;
import com.meiye.system.mapper.SysUserMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.meiye.api.mapper.MemberMapper;
import com.meiye.api.mapper.MemberLevelMapper;
import com.meiye.api.mapper.StoreMapper;

import static com.meiye.common.utils.SecurityUtils.*;

/**
 * 会员Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@Service
public class MemberServiceImpl
        extends ServiceImpl<MemberMapper, Member>
        implements MemberService
{
    @Autowired
    private MemberMapper memberMapper;

    @Autowired
    private MemberLevelMapper memberLevelMapper;

    @Autowired
    private MemberLevelService memberLevelService;

    @Autowired
    private SaleOrderService saleOrderService;

    @Autowired
    private MemberFeatureService memberFeatureService;

    @Autowired
    private MemberProductService memberProductService;
    
    @Autowired
    private StoreMapper storeMapper;
    
    @Autowired
    private SysUserMapper sysUserMapper;

    /**
     * 查询会员
     * 
     * @param id 会员主键
     * @return 会员
     */
    @Override
    public Member selectMemberById(Long id)
    {
        return memberMapper.selectMemberById(id);
    }

    /**
     * 查询会员列表
     * 
     * @param member 会员
     * @return 会员
     */
    @Override
    public List<Member> selectMemberList(Member member)
    {
        // 管理员/运营拥有全部权限
        if (hasRole("admin") || hasRole("operator")) {
            return memberMapper.selectMemberList(member);
        }
        // 主店经理权限处理
        else if (hasRole("mainStoreManager")) {
            SysUser user = sysUserMapper.selectUserById(getUserId());
            Long rootStoreId = user.getStoreId(); // 当前主店ID
            
            // 如果指定了要查询的店铺ID，需要校验是否为当前主店的子店铺
            if (member.getStoreId() != null) {
                Set<Long> sonStoreIds = getAllStoreIds(rootStoreId);
                // 判断指定的storeId是否在子店铺/主店ID集合中
                if (!sonStoreIds.contains(member.getStoreId())) {
                    // 不在集合中：清除storeId条件，仅按主店查询
                    Member notSonMember = new Member();
                    BeanUtils.copyProperties(member, notSonMember, "storeId");
                    notSonMember.setRootStoreId(rootStoreId);
                    return memberMapper.selectMemberList(notSonMember);
                } else {
                    // 在集合中：允许按指定storeId查询，但清除rootStoreId避免冲突
                    member.setRootStoreId(null);
                    return memberMapper.selectMemberList(member);
                }
            } else {
                // 未指定storeId：按主店及其所有子店查询
                member.setRootStoreId(rootStoreId);
                return memberMapper.selectMemberList(member);
            }
        }
        // 其他角色：只能查询自己所属店铺
        else {
            SysUser user = sysUserMapper.selectUserById(getUserId());
            member.setStoreId(user.getStoreId());
            return memberMapper.selectMemberList(member);
        }
    }

    /**
     * 获取主店及其所有子店铺的ID集合
     *
     * @param rootStoreId 主店ID
     * @return 主店及其所有子店铺的ID集合
     */
    private Set<Long> getAllStoreIds(Long rootStoreId) {
        List<Store> sonStores = storeMapper.selectSonsById(rootStoreId);
        Set<Long> sonStoreIds = sonStores.stream()
                .map(Store::getId)
                .collect(Collectors.toSet());
        // 添加主店自身
        sonStoreIds.add(rootStoreId);
        return sonStoreIds;
    }

    /**
     * 查询所有会员列表（不分页）
     *
     * @return 会员集合
     */
    @Override
    public List<Member> selectAllMember()
    {
        return memberMapper.selectAllMember();
    }

    /**
     * 获取会员详细信息（包含会员等级、最近订单、标签、推荐产品等）
     *
     * @param memberId 会员ID
     * @return 会员详细信息
     */
    @Override
    public Member getMemberDetailInfo(Long memberId) {
        // 获取会员基本信息
        Member member = selectMemberById(memberId);

        if (member != null) {
            // 获取会员等级信息
            if (member.getMemberLevelId() != null) {
                MemberLevel memberLevel = memberLevelService.selectMemberLevelById(member.getMemberLevelId());
                member.setMemberLevel(memberLevel);
            }
/*
            // 获取会员最近订单
            SaleOrder lastOrder = saleOrderService.getLastOrderByMemberId(memberId);
            member.setLastOrder(lastOrder);

            // 获取会员标签
            List<MemberFeature> tags = memberFeatureService.selectMemberFeaturesByMemberId(memberId);
            member.setTags(tags);

            // 获取会员推荐产品列表
            List<MemberProduct> advanceProductList = memberProductService.selectMemberProductsByMemberId(memberId);
            member.setAdvanceProductList(advanceProductList);
            member.setAdvanceProductList(advanceProductList);
 */
        }

        return member;
    }
    /**
     * 新增会员
     * 
     * @param member 会员
     * @return 结果
     */
    @Override
    public int insertMember(Member member) {
        // 确保 member_level_id 被正确赋值
        if (member.getMemberLevelId() == null) {
            throw new IllegalArgumentException("会员等级ID不能为空");
        }
        
        // 确保主店面ID不为空
        if (member.getRootStoreId() == null) {
            throw new IllegalArgumentException("主店面ID不能为空");
        }
        // 自动设置创建时间
        if (member.getCreatedAt() == null) {
            member.setCreatedAt(new Date());
        }
        
        return memberMapper.insertMember(member);
    }

    @Override
    public int addMember(Member member) {
        return insertMember(member);
    }

    /**
     * 修改会员
     * 
     * @param member 会员
     * @return 结果
     */
    @Override
    public int updateMember(Member member)
    {
        return memberMapper.updateMember(member);
    }

    /**
     * 批量删除会员
     * 
     * @param ids 需要删除的会员主键
     * @return 结果
     */
    @Override
    public int deleteMemberByIds(Long[] ids)
    {
        return memberMapper.deleteMemberByIds(ids);
    }

    /**
     * 删除会员信息
     * 
     * @param id 会员主键
     * @return 结果
     */
    @Override
    public int deleteMemberById(Long id)
    {
        return memberMapper.deleteMemberById(id);
    }

    /**
     * 查询会员等级选项列表
     *
     * @return 会员等级集合
     */
    @Override
    public List<MemberLevel> selectMemberLevelOptions()
    {
        return memberLevelMapper.selectMemberLevelList(new MemberLevel());
    }

    /**
     * 根据店面名称获取店面ID
     *
     * @param storeName 店面名称
     * @return 店面ID
     */
    @Override
    public Long getStoreIdByName(String storeName) {
        return memberMapper.getStoreIdByName(storeName);
    }

    /**
     * 获取主店面ID - 移除自动填充第一条数据的功能
     *
     * @return 主店面ID
     */
    @Override
    public Long getStoreRootId() {
        // 不再自动返回第一条数据，由Controller层处理
        return null;
    }
    
    @Override
    public String getMaxMemberSn(String snPrefix) {
        return memberMapper.getMaxMemberSn(snPrefix);
    }
    
    @Override
    public List<MemberVO> queryMemberList(MemberQuery query) {
        return memberMapper.queryMemberList(query);
    }

    @Override
    public Integer getYearMemberNum() {
        return  memberMapper.getYearMemberNum() ;
    }
    
    /**
     * 获取店铺列表
     *
     * @return 店铺列表
     */
    @Override
    public List<Store> getStoreList() {
        // 直接调用已有的StoreMapper方法获取店铺列表
        return storeMapper.selectStoreList(new Store());
    }
}