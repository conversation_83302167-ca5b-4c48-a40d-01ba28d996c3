package com.meiye.web.controller.api;

import com.meiye.api.dto.PendingOrderDto;
import com.meiye.api.service.SaleOrderService;
import com.meiye.common.core.controller.BaseController;
import com.meiye.common.core.domain.AjaxResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/Order")
public class OrderController extends BaseController {

    @Autowired
    SaleOrderService saleOrderService;


//    @GetMapping("/owner-order-list")
//    public AjaxResult addSaleOrder( OrderQuery query) {
//        query.setUserId(getUserId());
//        if (getStoreId()==null){
//            throw new RuntimeException("用户未选择门店");
//        }
//        dto.setStoreId(getStoreId());
//        return saleOrderService.addSaleOrderNew(dto);
//    }
}
