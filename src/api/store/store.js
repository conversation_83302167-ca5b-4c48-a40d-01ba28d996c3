import request from '@/utils/request'

// 查询店面列表
export function listStore(params) {
  return request({
    url: '/store/list',
    method: 'get',
    params
  });
}

export function storePositionList () {
  return request({
    url: '/store/current-department-position-list',
    method: 'get',

  })
}

export function storePositionListById (storeId) {
  return request({
    url: '/store/list-by-store-id/'+storeId,
    method: 'get',

  })
}

// 查询店面详细
export function getStore(id) {
  return request({
    url: '/store/' + id,
    method: 'get'
  })
}

// 查询店面列表
export function getMainList(query) {
  return request({
    url: '/store/option',
    method: 'get',
    params: query
  })
}

export function getUnallocated() {
  return request({
    url: '/store/unallocated',
    method: 'get',
  })
}


// 新增店面
export function addStore(data) {
  return request({
    url: '/store',
    method: 'post',
    data: data
  })
}

// 修改店面
export function updateStore(data) {
  return request({
    url: '/store',
    method: 'put',
    data: data
  })
}

// 删除店面
export function delStore(id) {
  return request({
    url: '/store/' + id,
    method: 'delete'
  })
}
