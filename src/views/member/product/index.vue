<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="会员姓名" prop="memberName">
        <el-input
          v-model="queryParams.memberName"
          placeholder="请输入会员姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="推荐项目" prop="storeProductId">
        <el-input
          v-model="queryParams.storeProductId"
          placeholder="请输入推荐项目"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- 店铺选择器 - 仅管理员和运营可见 -->
      <el-form-item label="注册店铺" prop="storeId" v-if="hasRole('admin') || hasRole('operator')">
        <el-select v-model="queryParams.storeId" placeholder="请选择注册店铺" clearable>
          <el-option
            v-for="store in storeOptions"
            :key="store.id"
            :label="store.name"
            :value="store.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['store:member:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['store:member:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['store:member:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['store:member:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="memberList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="id" align="center" prop="id" />
      <el-table-column label="会员姓名" align="center" prop="memberName" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.memberName || '未知会员' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="推荐项目" align="center" prop="storeProductId" />
      <el-table-column label="建议间隔时长" align="center" prop="cycleLength">
        <template slot-scope="scope">
          <span>{{ scope.row.cycleLength }}天</span>
        </template>
      </el-table-column>
      <el-table-column label="该项目最后执行时间" align="center" prop="lastDo" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.lastDo, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="注册会员信息的员工" align="center" prop="createdByEmployeeName" width="150">
        <template slot-scope="scope">
          <span>{{ scope.row.createdByEmployeeName || '未知员工' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="注册时间" align="center" prop="createdAt" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createdAt, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <!-- 显示店铺信息 -->
      <el-table-column label="注册店铺" align="center" prop="storeName" width="150" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['store:member:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['store:member:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改适于该会员的推荐项目对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="170px">
        <el-form-item label="会员" prop="memberId">
          <el-select
            v-model="form.memberId"
            placeholder="请选择会员"
            filterable
            remote
            :remote-method="searchMembers"
            :loading="memberLoading"
            clearable
            style="width: 100%"
            :disabled="isEdit"
          >
            <el-option
              v-for="member in memberOptions"
              :key="member.id"
              :label="`${member.name} `"
              :value="member.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="推荐项目" prop="storeProductId">
          <el-select
            v-model="form.storeProductId"
            placeholder="请选择推荐项目"
            filterable
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="product in productOptions"
              :key="product.id"
              :label="product.name"
              :value="product.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="建议间隔时长" prop="cycleLength">
          <el-input v-model="form.cycleLength" placeholder="请输入建议间隔时长" style="width: 100%">
            <template slot="append">天</template>
          </el-input>
        </el-form-item>
        <el-form-item label="该项目最后执行时间" prop="lastDo">
          <el-date-picker clearable
            v-model="form.lastDo"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择该项目最后执行时间"
            style="width: 100%">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listProduct, getProduct, delProduct, addProduct, updateProduct } from "@/api/member/product"
import { listMember as listMembers } from "@/api/store/member"
import { productOption } from "@/api/productManage/product"
import { listStore } from "@/api/store/store"

export default {
  name: "product",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 适于该会员的推荐项目表格数据
      memberList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否为编辑状态
      isEdit: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        memberId: null,
        memberName: null,
        storeProductId: null,
        cycleLength: null,
        lastDo: null,
        createdByEmployeeId: null,
        createdAt: null,
        storeId: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        memberId: [
          { required: true, message: "会员不能为空", trigger: "change" }
        ],
        storeProductId: [
          { required: true, message: "推荐项目不能为空", trigger: "blur" }
        ]
      },
      // 会员信息
      memberInfo: {
        id: null,
        name: ''
      },
      // 会员选项
      memberOptions: [],
      // 会员搜索加载状态
      memberLoading: false,
      // 项目选项
      productOptions: [],
      // 店铺选项
      storeOptions: []
    }
  },
  created() {
    // 检查路由参数
    if (this.$route.query.memberId) {
      this.queryParams.memberId = this.$route.query.memberId
      this.memberInfo = {
        id: this.$route.query.memberId,
        name: this.$route.query.memberName || ''
      }
    }
    this.getList()
    this.getMemberOptions()
    this.getProductOptions()
    // 获取店铺选项 - 仅管理员和运营可见
    if (this.hasRole('admin') || this.hasRole('operator')) {
      this.getStoreOptions()
    }
  },
  methods: {
    // 判断用户角色
    hasRole(role) {
      const roles = this.$store.getters && this.$store.getters.roles
      return roles && roles.some(r => r === role)
    },
    // 返回会员列表
    goBackToMemberList() {
      this.$router.push('/storeManage/member')
    },
    /** 查询适于该会员的推荐项目列表 */
    getList() {
      this.loading = true
      listProduct(this.queryParams).then(response => {
        this.memberList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        memberId: this.queryParams.memberId || null,
        storeProductId: null,
        cycleLength: null,
        lastDo: null,
        createdByEmployeeId: null,
        createdByEmployeeName: '',
        createdAt: null
      }
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      // 保持会员ID不被重置
      const memberId = this.queryParams.memberId
      this.resetForm("queryForm")
      this.queryParams.memberId = memberId
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.isEdit = false
      // 自动填充当前时间和当前用户信息
      this.form.createdAt = this.parseTime(new Date(), '{y}-{m}-{d}')
      this.form.createdByEmployeeId = this.$store.getters.id
      this.form.createdByEmployeeName = this.$store.getters.nickName
      // 如果有会员ID参数，自动填充
      if (this.queryParams.memberId) {
        this.form.memberId = this.queryParams.memberId
      }
      this.open = true
      this.title = "添加适于该会员的推荐项目"
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      this.isEdit = true
      const id = row.id || this.ids
      getProduct(id).then(response => {
        this.form = response.data
        // 确保显示员工姓名
        if (!this.form.createdByEmployeeName && this.form.createdByEmployeeId) {
          this.form.createdByEmployeeName = this.form.createdByEmployeeId
        }
        this.open = true
        this.title = "修改适于该会员的推荐项目"
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 确保提交时包含员工ID
          if (!this.form.createdByEmployeeId) {
            this.form.createdByEmployeeId = this.$store.getters.id
          }
          // 确保提交时包含创建时间
          if (!this.form.createdAt) {
            this.form.createdAt = this.parseTime(new Date(), '{y}-{m}-{d}')
          }

          if (this.form.id != null) {
            updateProduct(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            })
          } else {
            addProduct(this.form).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除适于该会员的推荐项目编号为"' + ids + '"的数据项？').then(function() {
        return delProduct(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('member/product/export', {
        ...this.queryParams
      }, `member_${new Date().getTime()}.xlsx`)
    },
    /** 获取会员选项 */
    getMemberOptions() {
      // 根据用户角色传递不同的参数
      const query = {}
      if (!(this.hasRole('admin') || this.hasRole('operator'))) {
        // 非管理员用户传递storeId参数
        query.storeId = this.$store.getters.storeId
      }
      listMembers(query).then(response => {
        this.memberOptions = response.rows || []
      })
    },
    /** 获取项目选项 */
    getProductOptions() {
      productOption().then(response => {
        this.productOptions = response.data || []
      })
    },
    /** 获取店铺选项 */
    getStoreOptions() {
      listStore().then(response => {
        this.storeOptions = response.rows || []
      })
    },
    /** 搜索会员 */
    searchMembers(query) {
      if (query !== '') {
        this.memberLoading = true
        // 根据用户角色传递不同的参数
        const searchQuery = { name: query }
        if (!(this.hasRole('admin') || this.hasRole('operator'))) {
          // 非管理员用户传递storeId参数
          searchQuery.storeId = this.$store.getters.storeId
        }
        listMembers(searchQuery).then(response => {
          this.memberOptions = response.rows || []
          this.memberLoading = false
        }).catch(() => {
          this.memberLoading = false
        })
      } else {
        this.getMemberOptions()
      }
    }
  }
}
</script>